const buildConfig = require("@clash-strategic/release-config");

module.exports = buildConfig({
  // Este paquete es para uso interno, no se publica en npm
  npmPublish: false,

  // Archivos que se incluirán en el commit de release
  gitAssets: ["CHANGELOG.md", "package.json", "package-lock.json"],

  // Mensaje personalizado para el commit de release
  gitMessage: "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}",
});
